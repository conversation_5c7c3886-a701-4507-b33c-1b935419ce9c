package service_test

import (
	"testing"
	"time"

	"sa-intranet/core/date"
	"sa-intranet/core/viewmodel"
	"sa-intranet/usecase/auth/service"
	"sa-intranet/usecase/auth/service/in"
	"sa-intranet/usecase/auth/service/out"

	"github.com/google/uuid"
	"github.com/samber/do"
)

type CreateTokenTestCase struct {
	name    string
	input   in.CreateTokenInput
	wantErr bool
	verify  func(t *testing.T, input in.CreateTokenInput, output viewmodel.Response[out.CreateTokenViewModel])
	setup   func(t *testing.T) uuid.UUID
}

func TestCreateToken(t *testing.T) {
	t.Parallel()

	svc := do.MustInvoke[*service.TokenService](injector)
	usersvc := do.MustInvoke[*service.UserService](injector)

	tests := []CreateTokenTestCase{
		{
			name: "create_token_expired_date",
			input: in.CreateTokenInput{
				ExpiresAt: date.Date{Time: time.Now().Add(-24 * time.Hour)}, // past date
			},
			wantErr: true,
		},
		{
			name: "create_token_expired_date",
			input: in.CreateTokenInput{
				ExpiresAt: date.Date{Time: time.Now().Add(-24 * time.Hour)}, // past date
			},
			wantErr: true,
		},
		{
			name: "empty_input",
			input: in.CreateTokenInput{
				ExpiresAt: date.Date{},
			},
			wantErr: true,
		},
		{
			name: "success",
			input: in.CreateTokenInput{
				ExpiresAt: date.Date{Time: time.Now().Add(24 * time.Hour)},
			},
			setup: func(t *testing.T) uuid.UUID {
				t.Helper()
				// Create a user to update
				createOutput := usersvc.CreateUser(in.CreateUserInput{
					Username: "token_username",
					Email:    "<EMAIL>",
					Role:     "guest",
				})
				if !createOutput.Success {
					t.Fatalf("Failed to create test user: %v", createOutput.Errors)
				}
				return createOutput.Data.ID
			},
			wantErr: false,
			verify: func(t *testing.T, input in.CreateTokenInput, output viewmodel.Response[out.CreateTokenViewModel]) {
				if output.Data.ExpiresAt.UTC().Format("2006-01-02") != input.ExpiresAt.Time.UTC().Format("2006-01-02") {
					t.Errorf("got expiresAt %v, want %v", output.Data.ExpiresAt.UTC().Format("2006-01-02"), input.ExpiresAt.Time.UTC().Format("2006-01-02"))
				}
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			var userID uuid.UUID
			if tt.setup != nil {
				userID = tt.setup(t)
			}

			tt.input.UserID = userID

			output := svc.CreateToken(tt.input)
			if (!output.Success) != tt.wantErr {
				t.Errorf("CreateToken() error = %v, wantErr %v", output.Errors, tt.wantErr)
				return
			}

			if !tt.wantErr && tt.verify != nil {
				tt.verify(t, tt.input, output)
			}
		})
	}
}

func TestListTokens(t *testing.T) {
	t.Parallel()

	svc := do.MustInvoke[*service.TokenService](injector)
	usersvc := do.MustInvoke[*service.UserService](injector)

	tests := []struct {
		name    string
		input   in.ListTokensInput
		wantErr bool
		verify  func(t *testing.T, input in.ListTokensInput, output viewmodel.Response[out.ListTokensViewModel])
		setup   func(t *testing.T) uuid.UUID
	}{
		{
			name: "success",
			input: in.ListTokensInput{
				UserID: uuid.New(),
			},
			setup: func(t *testing.T) uuid.UUID {
				t.Helper()
				// Create a user to list tokens for
				createOutput := usersvc.CreateUser(in.CreateUserInput{
					Username: "list_token_username",
					Email:    "<EMAIL>",
					Role:     "guest",
				})

				if !createOutput.Success {
					t.Fatalf("Failed to create test user: %v", createOutput.Errors)
				}
				createToken := svc.CreateToken(in.CreateTokenInput{
					UserID:    createOutput.Data.ID,
					ExpiresAt: date.Date{Time: time.Now().Add(24 * time.Hour)},
				})
				if !createToken.Success {
					t.Fatalf("Failed to create test token: %v", createToken.Errors)
				}
				return createOutput.Data.ID
			},
			wantErr: false,
			verify: func(t *testing.T, input in.ListTokensInput, output viewmodel.Response[out.ListTokensViewModel]) {
				if output.Data.Items[0].ExpiresAt.UTC().Format("2006-01-02") != time.Now().Add(24*time.Hour).UTC().Format("2006-01-02") {
					t.Errorf("got expiresAt %v, want %v", output.Data.Items[0].ExpiresAt.UTC().Format("2006-01-02"), time.Now().Add(24*time.Hour).UTC().Format("2006-01-02"))
				}

				if len(output.Data.Items) == 0 {
					t.Errorf("expected at least one token, got none")
				}
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			var userID uuid.UUID
			if tt.setup != nil {
				userID = tt.setup(t)
			}

			tt.input.UserID = userID

			output := svc.ListTokens(tt.input, 1, 10)
			if (!output.Success) != tt.wantErr {
				t.Errorf("ListTokens() error = %v, wantErr %v", output.Errors, tt.wantErr)
				return
			}

			if !tt.wantErr && tt.verify != nil {
				tt.verify(t, tt.input, output)
			}
		})
	}
}

func TestDeleteToken(t *testing.T) {
	t.Parallel()

	svc := do.MustInvoke[*service.TokenService](injector)
	usersvc := do.MustInvoke[*service.UserService](injector)

	tests := []struct {
		name    string
		input   in.DeleteTokenInput
		wantErr bool
		setup   func(t *testing.T) (uuid.UUID, uuid.UUID)
	}{
		{
			name:    "wrong_user_id",
			input:   in.DeleteTokenInput{},
			wantErr: true,
			setup: func(t *testing.T) (uuid.UUID, uuid.UUID) {
				t.Helper()
				// Create a user to delete tokens for
				createOutput := usersvc.CreateUser(in.CreateUserInput{
					Username: "delete_wrong_token_username",
					Email:    "<EMAIL>",
					Role:     "guest",
				})
				if !createOutput.Success {
					t.Fatalf("Failed to create test user: %v", createOutput.Errors)
				}
				createToken := svc.CreateToken(in.CreateTokenInput{
					UserID:    createOutput.Data.ID,
					ExpiresAt: date.Date{Time: time.Now().Add(24 * time.Hour)},
				})
				if !createToken.Success {
					t.Fatalf("Failed to create test token: %v", createToken.Errors)
				}
				return uuid.New(), createToken.Data.ID
			},
		},
		{
			name:  "success",
			input: in.DeleteTokenInput{},
			setup: func(t *testing.T) (uuid.UUID, uuid.UUID) {
				t.Helper()
				// Create a user to delete tokens for
				createOutput := usersvc.CreateUser(in.CreateUserInput{
					Username: "delete_token_username",
					Email:    "<EMAIL>",
					Role:     "guest",
				})

				if !createOutput.Success {
					t.Fatalf("Failed to create test user: %v", createOutput.Errors)
				}
				createToken := svc.CreateToken(in.CreateTokenInput{
					UserID:    createOutput.Data.ID,
					ExpiresAt: date.Date{Time: time.Now().Add(24 * time.Hour)},
				})
				if !createToken.Success {
					t.Fatalf("Failed to create test token: %v", createToken.Errors)
				}
				return createOutput.Data.ID, createToken.Data.ID
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			t.Parallel()

			var userID, tokenID uuid.UUID
			if tt.setup != nil {
				userID, tokenID = tt.setup(t)
			}

			tt.input.ID = tokenID
			tt.input.UserID = userID

			output := svc.DeleteToken(tt.input)
			if (!output.Success) != tt.wantErr {
				t.Errorf("DeleteToken() error = %v, wantErr %v", output.Errors, tt.wantErr)
				return
			}
		})
	}
}
